#!/bin/bash

# Script để mở tất cả các port cần thiết cho Supabase

set -e

echo "🚀 Mở tất cả các port cho Supabase..."

# Kiểm tra kubectl
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl không được tìm thấy. Vui lòng cài đặt kubectl."
    exit 1
fi

# Kiểm tra namespace
if ! kubectl get namespace supabase &> /dev/null; then
    echo "❌ Namespace supabase không tồn tại. Vui lòng cài đặt Supabase trước."
    exit 1
fi

# Mở port cho Supabase Studio
echo "📊 Mở port cho Supabase Studio (3000)..."
kubectl port-forward svc/supabase-studio 3000:3000 -n supabase &
STUDIO_PID=$!
echo "✅ Supabase Studio đang chạy ở http://localhost:3000"

# Mở port cho Kong API Gateway
echo "🌐 Mở port cho Kong API Gateway (8000)..."
kubectl port-forward svc/supabase-kong 8000:8000 -n supabase &
KONG_PID=$!
echo "✅ Kong API Gateway đang chạy ở http://localhost:8000"

# Mở port cho PostgreSQL
echo "🗄️  Mở port cho PostgreSQL (5432)..."
kubectl port-forward svc/supabase-db 5432:5432 -n supabase &
DB_PID=$!
echo "✅ PostgreSQL đang chạy ở localhost:5432"

# Mở port cho Storage
echo "📁 Mở port cho Storage (5000)..."
kubectl port-forward svc/supabase-storage 5000:5000 -n supabase &
STORAGE_PID=$!
echo "✅ Storage đang chạy ở http://localhost:5000"

# Mở port cho Realtime
echo "🔄 Mở port cho Realtime (4000)..."
kubectl port-forward svc/supabase-realtime 4000:4000 -n supabase &
REALTIME_PID=$!
echo "✅ Realtime đang chạy ở http://localhost:4000"

echo ""
echo "🎉 Tất cả các port đã được mở!"
echo ""
echo "📋 Thông tin kết nối:"
echo "- Supabase Studio: http://localhost:3000"
echo "- API Gateway: http://localhost:8000"
echo "- PostgreSQL: localhost:5432"
echo "- Storage: http://localhost:5000"
echo "- Realtime: http://localhost:4000"
echo ""
echo "⚠️  Nhấn Ctrl+C để dừng tất cả các port-forward"

# Trap để kill tất cả các process khi script bị dừng
trap 'kill $STUDIO_PID $KONG_PID $DB_PID $STORAGE_PID $REALTIME_PID 2>/dev/null' EXIT

# Chờ cho đến khi người dùng nhấn Ctrl+C
wait
