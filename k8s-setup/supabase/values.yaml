# C<PERSON>u hình <PERSON>base cho Kubernetes
# File này chứa các thiết lập tùy chỉnh cho Supabase

# Studio (Supabase Dashboard)
studio:
  image:
    tag: "latest"
  service:
    type: ClusterIP
    port: 3000
  resources:
    requests:
      memory: "256Mi"
      cpu: "250m"
    limits:
      memory: "512Mi"
      cpu: "500m"

# Kong API Gateway
kong:
  image:
    tag: "latest"
  service:
    type: ClusterIP
    port: 8000
  resources:
    requests:
      memory: "256Mi"
      cpu: "250m"
    limits:
      memory: "512Mi"
      cpu: "500m"

# PostgreSQL Database
postgresql:
  enabled: true
  auth:
    postgresPassword: "your-postgres-password"
    database: "postgres"
  primary:
    persistence:
      enabled: true
      size: 8Gi
    resources:
      requests:
        memory: "256Mi"
        cpu: "250m"
      limits:
        memory: "1Gi"
        cpu: "1000m"

# Auth service
auth:
  image:
    tag: "latest"
  service:
    type: ClusterIP
    port: 9999
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "250m"

# REST API service
rest:
  image:
    tag: "latest"
  service:
    type: ClusterIP
    port: 3000
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "250m"

# Realtime service
realtime:
  image:
    tag: "latest"
  service:
    type: ClusterIP
    port: 4000
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "250m"

# Storage service
storage:
  image:
    tag: "latest"
  service:
    type: ClusterIP
    port: 5000
  persistence:
    enabled: true
    size: 10Gi
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "250m"

# Meta service
meta:
  image:
    tag: "latest"
  service:
    type: ClusterIP
    port: 8080
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "250m"

# Edge Functions (Deno)
functions:
  image:
    tag: "latest"
  service:
    type: ClusterIP
    port: 9000
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "250m"

# Analytics
analytics:
  enabled: false

# Vector/Embeddings
vector:
  enabled: false

# Ingress configuration (tùy chọn)
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: supabase.local
      paths:
        - path: /
          pathType: Prefix
  tls: []
